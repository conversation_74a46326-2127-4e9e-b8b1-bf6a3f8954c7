import { Service } from "../models/service.entity.js";
import { SuperapiService } from "./superapi-service.js";
import { ResourceWithOptions } from "adminjs";
import { Components } from '../component-loader.js';

const superapiService = new SuperapiService();

export const ServiceResource: ResourceWithOptions = {
    resource: Service,
    options: {
      // 自定义详情页面字段显示顺序
      showProperties: [
        'id',
        'userid',
        'packageid',
        'domainstatus',
        'billingcycle',
        'regdate',
        'nextduedate',
        'firstpaymentamount',
        'amount',
        'username',
        'password',
        'notes',
        // 关联数据 - 放在最后
        'serviceInvoices'
      ],
      properties: {
        id: {
          isVisible: { list: true, filter: true, show: true, edit: false },
          type: 'number',
          isTitle: true
        },
        userid: {
          isVisible: { list: true, filter: true, show: true, edit: true },
          type: 'reference',
          reference: 'User'
        },
        packageid: {
          isVisible: { list: true, filter: true, show: true, edit: true },
          type: 'reference',
          reference: 'Product'
        },
        billingcycle: {
          isVisible: { list: true, filter: true, show: true, edit: true },
          type: 'string'
        },
        regdate: {
          isVisible: { list: true, filter: true, show: true, edit: false },
          type: 'date'
        },
        nextduedate: {
          isVisible: { list: true, filter: true, show: true, edit: true },
          type: 'datetime'
        },
        domainstatus: {
          isVisible: { list: true, filter: true, show: true, edit: true },
          type: 'string',
          availableValues: [
            { value: 'Pending', label: 'Pending' },
            { value: 'Active', label: 'Active' },
            { value: 'Suspended', label: 'Suspended' },
            { value: 'Terminated', label: 'Terminated' },
            { value: 'Cancelled', label: 'Cancelled' },
            { value: 'Fraud', label: 'Fraud' }
          ]
        },
        firstpaymentamount: {
          isVisible: { list: true, filter: true, show: true, edit: true },
          type: 'number'
        },
        amount: {
          isVisible: { list: true, filter: true, show: true, edit: true },
          type: 'number'
        },
        username: {
          isVisible: { list: false, filter: false, show: true, edit: true },
          type: 'string'
        },
        password: {
          isVisible: { list: false, filter: false, show: false, edit: true },
          type: 'password'
        },
        notes: {
          isVisible: { list: false, filter: false, show: true, edit: true },
          type: 'textarea'
        },
        serviceInvoices: {
          isVisible: { list: false, filter: false, show: true, edit: false },
          type: 'string',
          components: {
            show: Components.ServiceInvoices
          }
        }
      },
      actions: {
        edit: {
          after: async (response, request, context) => {
            const updatedRecord = response.record;
            if (request.payload.packageid) {
              await superapiService.syncServicePackage(updatedRecord.params.id, +request.payload.packageid);
            } 
            
            if (request.payload.nextduedate) {
              await superapiService.syncServiceExpireDate(updatedRecord.params.id, request.payload.nextduedate);
            }
            return response;
          },
        },
        superapi: {
          actionType: 'record',
          showInDrawer: true,
          isVisible: true,
          isAccessible: () => true,
          component: Components.SuperAPI,
          handler: async (request, response, context) => {
            const serviceId =
              context.record?.param?.('id') ||
              context.record?.params?.id ||
              request.params?.recordId ||
              request.query?.serviceId;
            const service = await superapiService.fetchService({ serviceId: serviceId });
            // 返回符合 RecordJSON 结构的对象
            return {
              record: {
                id: serviceId,
                params: service,
                populated: {},
                errors: {},
                recordActions: [],
                bulkActions: [],
                title: serviceId + '',
              }
            };
          },
        },
        testAction: {
          actionType: 'record',
          isVisible: true,
          isAccessible: () => true,
          handler: async () => ({ notice: { message: 'ok', type: 'success' } })
        },
        myDemoAction: {
          actionType: 'record',
          component: Components.MyDemoAction,
          showInDrawer: true,
          isVisible: true,
          isAccessible: () => true,
          handler: async (request, response, context) => {
            // 返回最简单的 record
            const serviceId =
              context.record?.param?.('id') ||
              context.record?.params?.id ||
              request.params?.recordId ||
              request.query?.serviceId;
            return {
              record: {
                id: serviceId,
                params: { demo: 'hello', id: serviceId },
                populated: {},
                errors: {},
                recordActions: [],
                bulkActions: [],
                title: serviceId + '',
              }
            };
          },
        },
      },
    },
  };